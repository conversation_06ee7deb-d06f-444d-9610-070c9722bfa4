apiVersion: v1
kind: ConfigMap
data:
  config.yaml: |
    server:
      http:
        network: "tcp"
        addr: 0.0.0.0:8000
        timeout: 10s
      grpc:
        network: "tcp"
        addr: 0.0.0.0:9000
        timeout: 10s
    data:
      run_mode: dev
      database:
        driver: postgres
        source: host=${DB_HOST} port=5432 user=${DB_USER} password=${DB_PWD} dbname=${DB_NAME} sslmode=disable
      redis:
        addr: ${REDIS_ADDR}
        pass: ${REDIS_PASS}
        write_timeout: 1s
      dj_config:
        max_page_size: 0
        specific_fields_required: false
        migrate_mode: UNLIMITED
      tenant_enabled: true
metadata:
  name: ${NAME}-config
  namespace: ${NAMESPACE}

---
apiVersion: v1
kind: Service
metadata:
  name: ${NAME}
  namespace: ${NAMESPACE}
spec:
  selector:
    app: ${NAME}
  ports:
    - name: grpc-internal
      port: 9000
      targetPort: 9000

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${NAME}
  namespace: ${NAMESPACE}
  labels:
    app: ${NAME}
spec:
  replicas: ${NUM}
  selector: 
    matchLabels:
      app: ${NAME}
  template:
    metadata:
      labels:
        app: ${NAME}
        log-format: json
        jwt-token-check: disabled
    spec:
      containers:
      - name: ${NAME}
        image: ${IMAGE}
        imagePullPolicy: Always
        command: ["./server"]
        args: ["-conf", "/configs/config.yaml"]
        ports:
        - containerPort: 8080
        resources:  # 资源限制的设置
          requests:  # 资源请求的限制
            cpu: ${REQUEST_CPU}  # Cpu的限制，单位为core数
            memory: ${REQUEST_MEM}  # 内存限制，单位可以为Mib/Gib
          limits:  # 资源限制的设置
            cpu: ${LIMIT_CPU}
            memory: ${LIMIT_MEM}
        volumeMounts:
        - name: ${NAME}-config
          mountPath: /configs
          readOnly: true
      volumes:
      - name: ${NAME}-config
        configMap:
          name: ${NAME}-config
