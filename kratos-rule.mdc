---
description: 
globs: 
alwaysApply: false
---
# Go-<PERSON><PERSON>s 微服务框架规范 v2.1

## 核心架构
```mermaid
graph TD
    A[API层] -->|HTTP/gRPC| B[Service层]
    B -->|用例调用| C[Biz层]
    C -->|数据访问| D[Data层]
    D -->|数据库驱动| E[(Database)]
    style A fill:#f9f,stroke:#333
    style B fill:#ccf,stroke:#333
    style C fill:#ff9,stroke:#333
    style D fill:#9f9,stroke:#333
```

## 项目目录规范

## 关键代码模板

### API定义
```protobuf:api/user/v1/user.proto
syntax = "proto3";
package user.v1;

service UserService {
  rpc CreateUser (CreateUserRequest) returns (User) {}
  rpc GetUser (GetUserRequest) returns (User) {}
}

message CreateUserRequest {
  string name = 1;
  string email = 2;
}

message GetUserRequest {
  int64 id = 1;
}

message User {
  int64 id = 1;
  string name = 2;
  string email = 3;
}
```

### 领域模型
```go:internal/domain/model/user.go
package model

import (
	"errors"
	"strings"
)

type User struct {
	ID    int64
	Name  string
	Email string
}

func (u *User) Validate() error {
	switch {
	case u.Name == "":
		return errors.New("用户名不能为空")
	case !strings.Contains(u.Email, "@"):
		return errors.New("邮箱格式错误")
	}
	return nil
}
```

## 依赖注入流程
```mermaid
sequenceDiagram
    participant M as main.go
    participant W as wire_gen.go
    participant D as Data
    participant B as Biz
    participant S as Service
    
    M->>W: 初始化配置
    W->>D: 创建数据实例
    W->>B: 创建Biz实例
    W->>S: 创建Service实例
    S->>M: 返回服务对象
```

## 开发工作流
1. 定义Proto接口 → `make api`
2. 编写Ent Schema → `make ent`
3. 实现Biz逻辑
4. 配置依赖注入 → `make wire`
5. 启动服务 → `kratos run`

## 代码生成命令表
| 命令         | 输入                  | 输出                    | 触发条件              |
|--------------|---------------------|-------------------------|---------------------|
| `make api`   | *.proto             | api/gen/...            | 修改proto文件时      |
| `make ent`   | ent/schema/*.go     | gen/database/ent/...        | 变更数据模型时        |
| `make wire`  | cmd/wire.go         | cmd/wire_gen.go        | 依赖关系变更时        |

## 异常处理规范
```go
// Data层返回原始错误
func (r *userRepo) FindByID(id int64) (*model.User, error) {
	user, err := r.data.db.User.Get(...)
	if ent.IsNotFound(err) {
		return nil, errors.Wrap(err, "用户不存在")
	}
	return user, err
}

// Biz层添加业务上下文
func (uc *UserUsecase) CreateUser(u *model.User) error {
	if err := u.Validate(); err != nil {
		return errors.Wrap(err, "业务校验失败")
	}
	// ...
}
```

## 监控指标要求
```go
// 必须包含的Prometheus指标
http_requests_total{method, path, status}
grpc_requests_total{method, code}
database_query_duration_seconds
```

## 版本控制策略
1. Proto版本：`/api/{service}/v1/`
2. Git标签格式：`service/user/v1.2.3`
3. 数据库迁移：每个版本对应独立的迁移文件

## 配置管理规范
```yaml:configs/config.yaml
server:
  http:
    addr: 0.0.0.0:8000
  grpc:
    addr: 0.0.0.0:9000
data:
  database:
    driver: mysql
    source: user:pass@tcp(localhost:3306)/test
  redis:
    addr: 127.0.0.1:6379
```

## Ent Schema示例
```go:ent/schema/user.go
type User struct {
	ent.Schema
}

func (User) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id"),
		field.String("name").MaxLen(50),
		field.String("email").Unique(),
		field.Time("created_at").Immutable(),
		field.Time("updated_at").UpdateDefault(time.Now),
	}
}
```

## 数据库迁移指南
```go:cmd/main.go
// 执行迁移
migrate, err := client.Schema.Create(
	ctx,
	schema.WithDropIndex(true),
	schema.WithDropColumn(true),
)
```

## Data层集成示例
```go:internal/data/user.go
func (r *userRepo) Save(u *model.User) (int64, error) {
	user, err := r.data.db.User.
		Create().
		SetName(u.Name).
		SetEmail(u.Email).
		Save(ctx)
	return user.ID, ConvertEntError(err)
}
```

## 性能优化建议
1. 批量操作：使用CreateBulk进行批量插入
2. 预加载关联：WithPosts()预加载关联数据
3. 分页查询：Offset().Limit().Order()
4. 缓存策略：Redis二级缓存
5. 连接池配置：
```yaml
max_idle_conns: 10
max_open_conns: 100
```

该文档已集成所有最佳实践，可作为项目开发的标准参考规范。