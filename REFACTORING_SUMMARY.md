# 函数命名规范重构总结

## 重构概述

本次重构针对 Kratos 项目中不同层级函数命名过于相似的问题，采用了基于 DDD（领域驱动设计）和业务语义的命名规范，显著提升了代码的可读性和维护性。

## 重构前后对比

### 1. Domain 层接口定义

#### 重构前
```go
// UserRepository 接口
type UserRepo interface {
    Create(context.Context, *User) (*User, error)
    Update(context.Context, *User) error
    Delete(context.Context, uint64) error
    List(context.Context, int32, int32) ([]*User, int, error)
}

// UserUsecase 接口
type UserUsecase interface {
    Create(ctx context.Context, u *User) (*User, error)
    Update(ctx context.Context, u *User) error
    Delete(ctx context.Context, id uint64) error
    List(ctx context.Context, pageNum, pageSize int32) ([]*User, int, error)
}
```

#### 重构后
```go
// UserRepo 用户仓储接口 - 使用 DDD 标准命名
type UserRepo interface {
    Save(context.Context, *User) (*User, error)                                    // 保存用户（新增/更新）
    FindByID(context.Context, uint64) (*User, error)                              // 根据ID查找用户
    FindAll(context.Context, int32, int32) ([]*User, int, error)                  // 查找用户列表
    Remove(context.Context, uint64) error                                         // 移除用户
    Update(context.Context, *User) error                                          // 更新用户信息
}

// UserUsecase 用户用例接口 - 使用业务语义命名
type UserUsecase interface {
    RegisterNewUser(ctx context.Context, u *User) (*User, error)                  // 注册新用户
    ModifyUserProfile(ctx context.Context, u *User) error                         // 修改用户资料
    DeactivateUser(ctx context.Context, id uint64) error                          // 停用用户
    SearchUsers(ctx context.Context, pageNum, pageSize int32) ([]*User, int, error) // 搜索用户
    FindUserByID(ctx context.Context, id uint64) (*User, error)                   // 根据ID查找用户
}
```

### 2. Data 层（Repository）重构

#### 重构前
```go
func (r *userRepo) Create(ctx context.Context, u *domain.User) (*domain.User, error)
func (r *userRepo) Update(ctx context.Context, u *domain.User) error
func (r *userRepo) Delete(ctx context.Context, id uint64) error
func (r *userRepo) List(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error)
```

#### 重构后
```go
func (r *userRepo) Save(ctx context.Context, u *domain.User) (*domain.User, error)        // DDD 标准命名
func (r *userRepo) FindByID(ctx context.Context, id uint64) (*domain.User, error)        // 根据ID查找
func (r *userRepo) FindAll(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) // 查找列表
func (r *userRepo) Remove(ctx context.Context, id uint64) error                          // 移除数据
func (r *userRepo) Update(ctx context.Context, u *domain.User) error                     // 更新数据
```

### 3. Biz 层（Usecase）重构

#### 重构前
```go
func (uc *UserUsecase) Create(ctx context.Context, u *domain.User) (*domain.User, error)
func (uc *UserUsecase) Update(ctx context.Context, u *domain.User) error
func (uc *UserUsecase) Delete(ctx context.Context, id uint64) error
func (uc *UserUsecase) List(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error)
```

#### 重构后
```go
func (uc *UserUsecase) RegisterNewUser(ctx context.Context, u *domain.User) (*domain.User, error)     // 注册新用户
func (uc *UserUsecase) ModifyUserProfile(ctx context.Context, u *domain.User) error                   // 修改用户资料
func (uc *UserUsecase) DeactivateUser(ctx context.Context, id uint64) error                           // 停用用户
func (uc *UserUsecase) SearchUsers(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) // 搜索用户
func (uc *UserUsecase) FindUserByID(ctx context.Context, id uint64) (*domain.User, error)            // 查找用户
```

### 4. Service 层重构

#### 重构前
```go
func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
    user, err := s.uc.Create(ctx, &domain.User{...})
    // ...
}
```

#### 重构后
```go
func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
    s.log.WithContext(ctx).Infof("接收到创建用户请求: Name=%s, Age=%d", req.Name, req.Age)
    
    user, err := s.uc.RegisterNewUser(ctx, &domain.User{...})
    // ...
}
```

## 重构收益

### 1. 可读性提升
- **业务语义清晰**：`RegisterNewUser` 比 `Create` 更能表达业务意图
- **层级职责明确**：通过函数名即可识别所属层级和职责
- **代码即文档**：函数名本身就是业务需求的体现

### 2. 维护性改善
- **减少命名冲突**：不同层级使用不同的命名模式
- **IDE 友好**：搜索和导航更加精确
- **新人友好**：降低理解成本

### 3. 架构一致性
- **符合 DDD 原则**：Repository 使用标准的 `Save`、`FindByID` 等命名
- **业务导向**：Usecase 层使用业务语义命名
- **Kratos 兼容**：与框架架构模式保持一致

## 命名规范总结

| 层级 | 命名风格 | 示例 | 说明 |
|------|----------|------|------|
| Service | 保持 API 语义 | `CreateUser`, `UpdateUser` | 与 Proto 定义保持一致 |
| Usecase | 业务语义 | `RegisterNewUser`, `ModifyUserProfile` | 体现真实业务操作 |
| Repository | DDD 标准 | `Save`, `FindByID`, `Remove` | 符合 DDD 仓储模式 |

## 验证结果

- ✅ 代码编译通过
- ✅ Wire 依赖注入正常工作
- ✅ 所有层级接口实现正确
- ✅ 日志记录完善
- ✅ 业务逻辑保持不变

## 后续建议

1. **团队培训**：向团队成员介绍新的命名规范
2. **文档更新**：更新开发规范文档
3. **代码审查**：在 PR 中检查命名规范遵循情况
4. **扩展应用**：将此命名规范应用到其他业务模块

这次重构成功地解决了函数命名相似性问题，为项目的长期维护奠定了良好基础。
