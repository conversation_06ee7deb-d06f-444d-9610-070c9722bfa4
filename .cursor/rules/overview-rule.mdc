---
description: 
globs: 
alwaysApply: false
---
# Kratos 微服务框架总览

Kratos 是一个轻量级的、面向云原生的 Go 微服务框架，使用以下规则确保开发标准一致。

## 核心规则

1. **[微服务规范](mdc:kratos-rule.mdc)** - 总体框架规范与开发流程
2. **[API 规范](mdc:api-rule.mdc)** - API 设计与 Proto 文件规范
3. **[数据库规范](mdc:database-rule.mdc)** - 数据库设计与 Ent ORM 使用
4. **[服务与业务规范](mdc:service-rule.mdc)** - 服务层和业务层开发指南
5. **[配置与部署规范](mdc:config-deploy-rule.mdc)** - 配置管理和部署策略
6. **[错误与日志规范](mdc:error-logging-rule.mdc)** - 错误处理与日志记录

## 架构概览

Kratos 框架采用 DDD (领域驱动设计) 思想，分为四层架构：

```mermaid
graph TD
    A[API层] -->|HTTP/gRPC| B[Service层]
    B -->|用例调用| C[Biz层]
    C -->|数据访问| D[Data层]
    D -->|数据库驱动| E[(Database)]
```

## 分层职责

| 层级        | 职责                                | 目录                   | 依赖方向           |
|------------|-------------------------------------|----------------------|-------------------|
| API 层     | 接口定义、协议转换                     | api/                 | ↓                |
| Service 层 | 请求处理、参数校验、转换                | internal/service/    | ↓                |
| Biz 层     | 业务逻辑、领域服务                     | internal/biz/        | ↓                |
| Domain 层  | 领域模型、领域规则                     | internal/domain/     | 无依赖            |
| Data 层    | 数据访问、持久化                       | internal/data/       | ↑ (依赖倒置)      |

## 开发工作流

1. 编写 Proto 文件定义 API → `make api`
2. 定义领域模型与接口
3. 实现业务逻辑 (Biz) 和数据访问 (Data)
4. 实现 Service 层接口
5. 注册依赖与服务 → `make wire`
6. 启动服务 → `kratos run`

## 代码生成命令

| 命令         | 功能                     | 触发条件              |
|--------------|------------------------|---------------------|
| `make api`   | 生成 API 代码           | 修改 Proto 文件      |
| `make ent`   | 生成 ORM 代码           | 修改 Ent Schema     |
| `make wire`  | 生成依赖注入代码         | 修改依赖关系         |

## 项目目录结构

```
myapp/
├── api/                # API定义
├── cmd/                # 入口程序
├── configs/            # 配置文件
├── gen/                # 生成代码
├── internal/           # 内部代码
│   ├── biz/           # 业务逻辑
│   ├── data/          # 数据访问
│   ├── domain/        # 领域模型
│   ├── service/       # 服务实现
│   └── server/        # 服务注册
├── third_party/        # 第三方依赖
└── deploy/             # 部署配置
```
