---
description:
globs:
alwaysApply: false
---
# 错误处理与日志规范

## 错误处理原则

Kratos 项目使用统一的错误处理机制，以确保微服务返回一致的错误格式。

### 错误定义

```go:internal/pkg/errors/errors.go
package errors

import (
	"github.com/go-kratos/kratos/v2/errors"
)

var (
	// 系统级错误 (5xxxxx)
	ErrSystem          = errors.InternalServer("SYSTEM_ERROR", "系统内部错误")
	ErrServiceUnavailable = errors.ServiceUnavailable("SERVICE_UNAVAILABLE", "服务暂时不可用")
	ErrTimeout         = errors.DeadlineExceeded("TIMEOUT", "请求超时")
	
	// 认证授权错误 (4xxxxx)
	ErrUnauthorized    = errors.Unauthorized("UNAUTHORIZED", "未授权访问")
	ErrForbidden       = errors.Forbidden("FORBIDDEN", "禁止访问")
	
	// 业务错误 (4xxxxx)
	ErrInvalidArgument = errors.BadRequest("INVALID_ARGUMENT", "请求参数无效")
	ErrNotFound        = errors.NotFound("NOT_FOUND", "资源不存在")
	ErrAlreadyExists   = errors.Conflict("ALREADY_EXISTS", "资源已存在")
)

// 为错误添加详细信息
func WithDetails(err *errors.Error, details ...interface{}) *errors.Error {
	return err.WithMetadata(errors.Metadata{
		"details": details,
	})
}
```

### 错误使用

```go:internal/service/user.go
import "kratos-layout/internal/pkg/errors"

func (s *UserService) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.GetUserReply, error) {
	if req.Id <= 0 {
		return nil, errors.WithDetails(errors.ErrInvalidArgument, "用户ID必须大于0")
	}
	
	user, err := s.uc.GetUser(ctx, req.Id)
	if err != nil {
		// 根据业务错误类型转换为响应错误
		if errors.Is(err, domain.ErrUserNotFound) {
			return nil, errors.WithDetails(errors.ErrNotFound, "用户不存在")
		}
		s.log.Errorf("获取用户失败: %v", err)
		return nil, errors.ErrSystem
	}
	
	return &pb.GetUserReply{
		User: &pb.User{
			Id:    user.ID,
			Name:  user.Name,
			Email: user.Email,
		},
	}, nil
}
```

### 错误传递原则

1. **数据层（Data）**：返回原始错误，不做错误转换
2. **业务层（Biz）**：添加业务上下文，使用领域错误
3. **服务层（Service）**：将业务错误转换为 HTTP/gRPC 响应错误
4. **中间件（Middleware）**：处理通用错误如认证、限流等

## 日志规范

Kratos 使用结构化日志，统一日志格式并支持多种输出方式。

### 日志初始化

```go:cmd/server/main.go
import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
)

func main() {
	// 初始化日志
	logger := log.With(
		log.NewStdLogger(os.Stdout),
		"service.name", "user-service",
		"service.version", "v1.0.0",
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)
	
	// 应用日志
	app, err := initApp(bc.Server, bc.Data, logger)
	// ...
}
```

### 日志使用

```go:internal/biz/user.go
import "github.com/go-kratos/kratos/v2/log"

type UserUsecase struct {
	repo UserRepo
	log  *log.Helper
}

func NewUserUsecase(repo UserRepo, logger log.Logger) *UserUsecase {
	return &UserUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (uc *UserUsecase) CreateUser(ctx context.Context, u *domain.User) (int64, error) {
	uc.log.WithContext(ctx).Infof("创建用户：%s", u.Name)
	
	if err := u.Validate(); err != nil {
		uc.log.WithContext(ctx).Errorf("用户数据验证失败: %v", err)
		return 0, err
	}
	
	id, err := uc.repo.Save(ctx, u)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("保存用户失败: %v", err)
		return 0, err
	}
	
	uc.log.WithContext(ctx).Infof("用户创建成功: %d", id)
	return id, nil
}
```

### 日志级别使用规范

1. **DEBUG**：详细调试信息，仅在开发环境使用
   ```go
   log.Debugf("处理请求参数: %v", req)
   ```

2. **INFO**：正常操作信息，记录关键业务流程
   ```go
   log.Infof("用户 %d 登录成功", userID)
   ```

3. **WARN**：不影响系统运行的警告信息
   ```go
   log.Warnf("缓存未命中，回退到数据库查询: %s", key)
   ```

4. **ERROR**：影响功能但不影响系统运行的错误
   ```go
   log.Errorf("用户创建失败: %v", err)
   ```

5. **FATAL**：导致系统无法正常运行的严重错误
   ```go
   log.Fatalf("数据库连接失败: %v", err)
   ```

### 日志内容规范

1. **包含上下文**：使用 `WithContext()` 传递请求上下文
2. **结构化日志**：使用字段而非简单字符串
3. **敏感信息处理**：密码、令牌等信息不应记录
4. **异常完整性**：记录完整的错误信息和堆栈
5. **请求日志**：记录请求ID、用户ID等关联信息

```go
// 推荐的日志记录方式
log.WithContext(ctx).With(
	"user_id", userID,
	"request_id", requestID,
	"action", "create_user",
).Infof("操作执行结果: %s", result)
```
