---
description:
globs:
alwaysApply: false
---
# 配置与部署规范

## 配置管理

Kratos 项目使用统一的配置管理机制，配置文件位于 `configs` 目录。

### 配置文件结构

```yaml:configs/config.yaml
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
data:
  database:
    driver: mysql
    source: root:password@tcp(127.0.0.1:3306)/test?parseTime=true
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600s
  redis:
    addr: 127.0.0.1:6379
    read_timeout: 0.2s
    write_timeout: 0.2s
    db: 0
```

### 配置加载

配置通过 `internal/conf` 目录下的结构体加载：

```go:internal/conf/conf.proto
syntax = "proto3";

package kratos.api;

option go_package = "kratos-layout/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
}

message Server {
  message HTTP {
    string addr = 1;
    google.protobuf.Duration timeout = 2;
  }
  message GRPC {
    string addr = 1;
    google.protobuf.Duration timeout = 2;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    int32 max_idle_conns = 3;
    int32 max_open_conns = 4;
    google.protobuf.Duration conn_max_lifetime = 5;
  }
  message Redis {
    string addr = 1;
    google.protobuf.Duration read_timeout = 2;
    google.protobuf.Duration write_timeout = 3;
    int32 db = 4;
  }
  Database database = 1;
  Redis redis = 2;
}
```

### 配置使用

```go:cmd/server/main.go
func main() {
	// 加载配置
	flag.Parse()
	cfg := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	if err := cfg.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := cfg.Scan(&bc); err != nil {
		panic(err)
	}

	// 使用配置
	app, err := initApp(bc.Server, bc.Data)
	if err != nil {
		panic(err)
	}
	// ...
}
```

## 部署规范

### Docker 部署

项目根目录包含 Dockerfile 用于容器化部署：

```dockerfile:Dockerfile
FROM golang:1.18-alpine AS builder

WORKDIR /app

COPY . .

RUN go build -o /app/server ./cmd/server

FROM alpine:latest

WORKDIR /app

COPY --from=builder /app/server /app/server
COPY --from=builder /app/configs /app/configs

EXPOSE 8000 9000

ENTRYPOINT ["/app/server", "-conf", "/app/configs"]
```

### Kubernetes 部署

项目 `deploy` 目录包含 Kubernetes 部署配置：

```yaml:deploy/kubernetes/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kratos-demo
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: kratos-demo
  template:
    metadata:
      labels:
        app: kratos-demo
    spec:
      containers:
      - name: kratos-demo
        image: kratos-demo:latest
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 9000
          name: grpc
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 3
        env:
        - name: MYSQL_DSN
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: dsn
```

## 环境变量

应用支持通过环境变量覆盖配置：

```go:internal/conf/config.go
package conf

import (
	"os"
	"strconv"
	"strings"
)

// 环境变量优先级高于配置文件
func ProcessEnv(c *Bootstrap) {
	// 数据库配置
	if dsn := os.Getenv("MYSQL_DSN"); dsn != "" {
		c.Data.Database.Source = dsn
	}
	
	// 服务端口
	if port := os.Getenv("HTTP_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			c.Server.Http.Addr = strings.Replace(c.Server.Http.Addr, ":8000", ":"+port, 1)
		}
	}
}
```

## 监控与可观测性

### Prometheus 指标

```go:internal/server/http.go
import "github.com/prometheus/client_golang/prometheus/promhttp"

// 注册 Prometheus 指标
http.Handle("/metrics", promhttp.Handler())
```

### Tracing 集成

```go:internal/server/server.go
import "go.opentelemetry.io/otel"

// 初始化 Tracer
tp := trace.NewTracerProvider(
	trace.WithSampler(trace.AlwaysSample()),
	trace.WithBatcher(exporter),
)
otel.SetTracerProvider(tp)
```
