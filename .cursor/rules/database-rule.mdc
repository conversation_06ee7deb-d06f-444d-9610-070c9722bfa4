---
description:
globs:
alwaysApply: false
---
# 数据库设计与访问规范

## Ent Schema 使用规范

Kratos 项目使用 Ent 作为 ORM 框架，Schema 文件定义在 `internal/data/schema` 目录下。

### Schema 设计原则

1. **领域驱动设计**：Schema 应反映业务领域模型，而非数据库表结构
2. **单一职责**：每个 Schema 文件只定义一个实体及其关系
3. **显式验证**：所有业务规则应通过验证器明确定义
4. **关系明确**：使用 Edges() 方法清晰定义实体间关系

### Schema 示例

```go:internal/data/schema/user.go
package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"time"
)

// User holds the schema definition for the User entity.
type User struct {
	ent.Schema
}

// Fields of the User.
func (User) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id"),
		field.String("name").MaxLen(50).Comment("用户名"),
		field.String("email").Unique().Comment("邮箱地址"),
		field.Time("created_at").Immutable().Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").UpdateDefault(time.Now).Comment("更新时间"),
	}
}

// Edges of the User.
func (User) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("posts", Post.Type),
		edge.From("groups", Group.Type).Ref("users"),
	}
}

// Indexes of the User.
func (User) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("name", "email"),
	}
}
```

## 数据访问层（Data Layer）

数据访问层位于 `internal/data` 目录，负责具体的数据库操作。

### 目录结构

```
internal/
└── data/
    ├── data.go         # 数据层依赖注入
    ├── user.go         # 用户数据访问实现
    └── schema/         # Ent Schema 定义
```

### Repository 实现示例

```go:internal/data/user.go
package data

import (
	"context"
	"kratos-layout/internal/biz"
	"kratos-layout/internal/domain/model"

	"github.com/go-kratos/kratos/v2/log"
)

type userRepo struct {
	data *Data
	log  *log.Helper
}

// NewUserRepo 创建用户仓储实现
func NewUserRepo(data *Data, logger log.Logger) biz.UserRepo {
	return &userRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// Save 保存用户
func (r *userRepo) Save(ctx context.Context, u *model.User) (int64, error) {
	po, err := r.data.db.User.
		Create().
		SetName(u.Name).
		SetEmail(u.Email).
		Save(ctx)
	if err != nil {
		return 0, err
	}
	return po.ID, nil
}

// FindByID 根据ID查找用户
func (r *userRepo) FindByID(ctx context.Context, id int64) (*model.User, error) {
	po, err := r.data.db.User.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return &model.User{
		ID:    po.ID,
		Name:  po.Name,
		Email: po.Email,
	}, nil
}
```

## 数据迁移与生成

使用 `make ent` 命令生成 Ent 相关代码和执行迁移：

```go:cmd/server/main.go
// 执行迁移
if err := client.Schema.Create(
	context.Background(),
	schema.WithDropIndex(true),
	schema.WithDropColumn(true),
); err != nil {
	log.Fatal(err)
}
```

## 性能优化建议

1. **批量操作**：使用 CreateBulk() 进行批量插入
2. **预加载关联**：WithPosts() 预加载关联数据
3. **分页查询**：Offset().Limit().Order() 进行分页
4. **查询优化**：Select() 选择需要的字段
5. **连接池配置**：设置合理的连接数

```yaml:configs/config.yaml
data:
  database:
    driver: mysql
    source: root:password@tcp(127.0.0.1:3306)/test?parseTime=true
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600s
```
