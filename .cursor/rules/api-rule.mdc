---
description:
globs:
alwaysApply: false
---
# API 开发规范

## Proto 文件规范

API 定义遵循 Protocol Buffers 格式，存放在 `api` 目录下。

### 目录结构
```
api/
├── user/               # 服务名称
│   └── v1/            # 版本号
│       ├── user.proto  # 服务定义
│       └── error.proto # 错误码定义
```

### 示例定义
```protobuf:api/user/v1/user.proto
syntax = "proto3";

package user.v1;

import "google/api/annotations.proto";

option go_package = "github.com/go-kratos/kratos-layout/api/user/v1;v1";

service UserService {
  rpc CreateUser (CreateUserRequest) returns (CreateUserReply) {
    option (google.api.http) = {
      post: "/v1/users"
      body: "*"
    };
  }
  
  rpc GetUser (GetUserRequest) returns (GetUserReply) {
    option (google.api.http) = {
      get: "/v1/users/{id}"
    };
  }
}

message CreateUserRequest {
  string name = 1;
  string email = 2;
}

message CreateUserReply {
  int64 id = 1;
}

message GetUserRequest {
  int64 id = 1;
}

message GetUserReply {
  User user = 1;
}

message User {
  int64 id = 1;
  string name = 2;
  string email = 3;
}
```

## 错误码定义规范

```protobuf:api/user/v1/error.proto
syntax = "proto3";

package user.v1;

option go_package = "github.com/go-kratos/kratos-layout/api/user/v1;v1";

enum ErrorReason {
  // 0 为保留值，代表成功
  SUCCESS = 0;
  // 通用错误，从 500001 开始
  UNKNOWN_ERROR = 500001;
  // 业务错误，从 400001 开始
  USER_NOT_FOUND = 400001;
  INVALID_USER_INPUT = 400002;
}
```

## API 生成指南

使用 `make api` 命令生成 API 相关代码：
1. 生成目录：`gen/api/`
2. 包含：Go 结构体、gRPC 服务定义、HTTP 路由

## 版本管理

1. API 版本通过目录区分：`v1`, `v2` 等
2. 重大变更需创建新版本目录
3. 向后兼容变更可在现有版本内进行
