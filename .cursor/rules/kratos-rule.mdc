---
description: 微服务框架规范
globs: 
alwaysApply: false
---
# Go-Kratos 微服务框架规范 v2.1

## 核心架构
```mermaid
graph TD
    A[API层] -->|HTTP/gRPC| B[Service层]
    B -->|用例调用| C[Biz层]
    C -->|数据访问| D[Data层]
    D -->|数据库驱动| E[(Database)]
    style A fill:#f9f,stroke:#333
    style B fill:#ccf,stroke:#333
    style C fill:#ff9,stroke:#333
    style D fill:#9f9,stroke:#333
```

## 项目目录规范
myapp/
├── api/
│ └── user/
│		└── v1/
│			└── user.proto # Protobuf接口定义
├── cmd/myapp/
│ ├── main.go # 服务入口
│ └── wire.go # 依赖注入配置
│ └── wire_gen.go # 依赖注入生成文件
├── configs/
│ └── config.yaml # 本地开发配置
├── gen/
│ └── database/ent/ # ent自动生成的ORM代码
│ └── api/ # protobuf自动生成的HTTP,GRPC代码
├── internal/
│ ├── data/ # 业务数据访问，包含 cache、db 等封装, 实现了 domain 层定义的 repo 接口
│ │ └── user_repo.go
│ │ └── data.go # var ProviderSet 定义data层依赖注入
│ ├── biz/ # 业务逻辑的组装层
│ │ └── user.go
│ │ └── biz.go # var ProviderSet 定义biz层依赖注入
│ └── domain/ # DDD的domain层，data类似DDD的repo,repo的接口在这里定义,使用依赖倒置的原则
│   └── user.go
│ ├── service/ # 实现了 api 定义的服务层，类似 DDD 的 application 层，处理 DTO 到 biz 领域实体的转换（DTO -> DO），同时协同各类 biz 交互，但是不应处理复杂逻辑
│ │ └── user.go 
│ │ └── service.go # var ProviderSet 定义service层依赖注入
│ ├── server/ # 为http和grpc实例的创建和配置,以及注册对应的service
│ │ └── http.go
│ │ └── grpc.go 

## 关键代码模板

### API定义
```protobuf:api/user/v1/user.proto
syntax = "proto3";
package user.v1;

service UserService {
  rpc CreateUser (CreateUserRequest) returns (User) {}
  rpc GetUser (GetUserRequest) returns (User) {}
}

message CreateUserRequest {
  string name = 1;
  string email = 2;
}

message GetUserRequest {
  int64 id = 1;
}

message User {
  int64 id = 1;
  string name = 2;
  string email = 3;
}
```

### 领域模型
```go:internal/domain/user.go
package domain

import (
	"errors"
	"strings"
)

type User struct {
	ID    int64
	Name  string
	Email string
}

// UserRepository 用户仓储接口
type UserRepo interface {
	Create(context.Context, *User) (*User, error)
	Update(context.Context, *User) error
	Delete(context.Context, uint64) error
	List(context.Context, int32, int32) ([]*User, int, error)
}

// UserUsecase 定义用例接口
type UserUsecase interface {
	Create(ctx context.Context, u *User) (*User, error)
	Update(ctx context.Context, u *User) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, pageNum, pageSize int32) ([]*User, int, error)
}

func (u *User) Validate() error {
	switch {
	case u.Name == "":
		return errors.New("用户名不能为空")
	case !strings.Contains(u.Email, "@"):
		return errors.New("邮箱格式错误")
	}
	return nil
}
```

## 依赖注入流程
```mermaid
sequenceDiagram
    participant M as main.go
    participant W as wire_gen.go
    participant D as Data
    participant B as Biz
    participant S as Service
    participant R as Server
    
    M->>W: 初始化配置
    W->>D: 创建数据实例
    W->>B: 创建Biz实例
    W->>S: 创建Service实例
    S->>R: 返回服务对象, 将服务注册到对应的路由,如http和grpc
		R->>M: 服务启动
```

## 开发工作流
1. 定义Proto接口 → `make api`
2. 编写Ent Schema → `make ent`
3. 实现biz, domain, service, data层逻辑
4. 注册service 服务到路由 internal/server/http, internal/server/grpc
5. 配置各层依赖注入 → `make wire`
6. 启动服务 → `kratos run`

## 代码生成命令表
| 命令         | 输入                | 输出                   | 触发条件              |
|--------------|---------------------|------------------------|-----------------------|
| `make api`   | *.proto             | gen/api/...            | 修改proto文件时       |
| `make ent`   | ent/schema/*.go     | gen/database/ent/...   | 变更数据模型时        |
| `make wire`  | cmd/*/wire.go       | cmd/*/wire_gen.go      | 依赖关系变更时        |

## 异常处理规范
```go
// Data层返回原始错误
func (r *userRepo) FindByID(id int64) (*domain.User, error) {
	user, err := r.data.db.User.Get(...)
	if ent.IsNotFound(err) {
		return nil, errors.Wrap(err, "用户不存在")
	}
	return user, err
}

// Biz层添加业务上下文
func (uc *UserUsecase) CreateUser(u *domain.User) error {
	if err := u.Validate(); err != nil {
		return errors.Wrap(err, "业务校验失败")
	}
	// ...
}
```

## 配置管理规范
```yaml:configs/config.yaml
server:
  http:
    addr: 0.0.0.0:8000
  grpc:
    addr: 0.0.0.0:9000
data:
  database:
    driver: postgres
    source: host=127.0.0.1 port=5432 user=kratos password=kratos dbname=kratos sslmode=disable
  redis:
    addr: 127.0.0.1:6379
```

## Ent Schema示例
```go:ent/schema/user.go
type User struct {
	ent.Schema
}

func (User) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id"),
		field.String("name").MaxLen(50),
		field.String("email").Unique(),
		field.Time("created_at").Immutable(),
		field.Time("updated_at").UpdateDefault(time.Now),
	}
}
```

## Data层集成示例
```go:internal/data/user_repo.go
func (r *userRepo) Save(u *domain.User) (int64, error) {
	user, err := r.data.db.User.
		Create().
		SetName(u.Name).
		SetEmail(u.Email).
		Save(ctx)
	return user.ID, ConvertEntError(err)
}
```

该文档已集成所有最佳实践，可作为项目开发的标准参考规范。
