---
description:
globs:
alwaysApply: false
---
# 服务层与业务层规范

## 架构分层

Kratos 框架采用清晰的四层架构：

```mermaid
graph TD
    A[API层] -->|HTTP/gRPC| B[Service层]
    B -->|用例调用| C[Biz层]
    C -->|数据访问| D[Data层]
    D -->|数据库驱动| E[(Database)]
    style A fill:#f9f,stroke:#333
    style B fill:#ccf,stroke:#333
    style C fill:#ff9,stroke:#333
    style D fill:#9f9,stroke:#333
```

## 业务逻辑层（Biz Layer）

业务逻辑层位于 `internal/biz` 目录，包含核心业务逻辑，不依赖于具体实现细节。

### 目录结构

```
internal/
└── biz/
    ├── biz.go          # 业务层依赖注入
    └── user.go         # 用户业务逻辑
```

### 业务接口定义

```go:internal/biz/user.go
package biz

import (
	"context"
	"kratos-layout/internal/domain/model"

	"github.com/go-kratos/kratos/v2/log"
)

// UserRepo 用户仓储接口
type UserRepo interface {
	Save(context.Context, *model.User) (int64, error)
	FindByID(context.Context, int64) (*model.User, error)
}

// UserUsecase 用户用例
type UserUsecase struct {
	repo UserRepo
	log  *log.Helper
}

// NewUserUsecase 创建用户用例
func NewUserUsecase(repo UserRepo, logger log.Logger) *UserUsecase {
	return &UserUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// CreateUser 创建用户
func (uc *UserUsecase) CreateUser(ctx context.Context, u *model.User) (int64, error) {
	// 业务逻辑验证
	if err := u.Validate(); err != nil {
		return 0, err
	}
	
	// 持久化
	return uc.repo.Save(ctx, u)
}

// GetUser 获取用户
func (uc *UserUsecase) GetUser(ctx context.Context, id int64) (*model.User, error) {
	return uc.repo.FindByID(ctx, id)
}
```

## 服务层（Service Layer）

服务层位于 `internal/service` 目录，负责API实现和输入/输出转换。

### 目录结构

```
internal/
└── service/
    ├── service.go      # 服务层依赖注入
    └── user.go         # 用户服务实现
```

### 服务实现示例

```go:internal/service/user.go
package service

import (
	"context"

	pb "kratos-layout/gen/api/user/v1"
	"kratos-layout/internal/biz"
	"kratos-layout/internal/domain/model"

	"github.com/go-kratos/kratos/v2/log"
)

// UserService 用户服务实现
type UserService struct {
	pb.UnimplementedUserServiceServer
	
	uc  *biz.UserUsecase
	log *log.Helper
}

// NewUserService 创建用户服务
func NewUserService(uc *biz.UserUsecase, logger log.Logger) *UserService {
	return &UserService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// CreateUser 创建用户API实现
func (s *UserService) CreateUser(ctx context.Context, req *pb.CreateUserRequest) (*pb.CreateUserReply, error) {
	// 请求DTO转领域模型
	user := &model.User{
		Name:  req.Name,
		Email: req.Email,
	}
	
	// 调用业务逻辑
	id, err := s.uc.CreateUser(ctx, user)
	if err != nil {
		return nil, err
	}
	
	// 返回响应
	return &pb.CreateUserReply{
		Id: id,
	}, nil
}

// GetUser 获取用户API实现
func (s *UserService) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.GetUserReply, error) {
	// 调用业务逻辑
	user, err := s.uc.GetUser(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	
	// 领域模型转响应DTO
	return &pb.GetUserReply{
		User: &pb.User{
			Id:    user.ID,
			Name:  user.Name,
			Email: user.Email,
		},
	}, nil
}
```

## 错误处理规范

服务层应使用统一的错误转换机制：

```go
import "github.com/go-kratos/kratos/v2/errors"

// 定义错误码
var (
	ErrUserNotFound     = errors.NotFound("USER_NOT_FOUND", "用户不存在")
	ErrInvalidUserInput = errors.BadRequest("INVALID_USER_INPUT", "无效的用户输入")
)

// 返回标准错误
if user == nil {
	return nil, ErrUserNotFound
}
```

## 依赖注入（Wire）

使用 Wire 框架进行依赖注入配置：

```go:cmd/server/wire.go
// +build wireinject

package main

import (
	"kratos-layout/internal/biz"
	"kratos-layout/internal/data"
	"kratos-layout/internal/service"
	"kratos-layout/internal/server"

	"github.com/google/wire"
)

// initApp 初始化应用
func initApp() (*App, error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, newApp))
}
```

## 领域模型（Domain Model）

领域模型位于 `internal/domain/model` 目录，包含核心业务实体。

```go:internal/domain/model/user.go
package model

import (
	"errors"
	"strings"
)

// User 用户领域模型
type User struct {
	ID    int64
	Name  string
	Email string
}

// Validate 验证用户数据
func (u *User) Validate() error {
	switch {
	case u.Name == "":
		return errors.New("用户名不能为空")
	case !strings.Contains(u.Email, "@"):
		return errors.New("邮箱格式错误")
	}
	return nil
}
```
