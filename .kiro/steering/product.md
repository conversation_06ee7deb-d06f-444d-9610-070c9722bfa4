# Product Overview

This is a Kratos-based microservice template project that provides a user management service with multi-tenant support. The service demonstrates a clean architecture pattern with gRPC and HTTP APIs, database integration using Ent ORM, and message queue capabilities with Kafka.

## Key Features
- User CRUD operations (Create, Read, Update, Delete)
- Multi-tenant architecture support
- Dual protocol support (gRPC + HTTP REST)
- Database abstraction with PostgreSQL/MySQL support
- Message queue integration with Kafka
- Low-code configuration capabilities
- OpenAPI/Swagger documentation generation

## Service Capabilities
- RESTful HTTP API endpoints under `/v1/users`
- gRPC service interface for high-performance communication
- Tenant-aware data isolation
- Configurable pagination and field selection
- Database migration management
- Distributed tracing and logging