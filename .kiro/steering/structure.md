# Project Structure

## Architecture Pattern
This project follows **Clean Architecture** principles with clear separation of concerns across layers:
- **API Layer**: Protocol buffer definitions and generated code
- **Service Layer**: Business logic orchestration and API implementation
- **Business Layer**: Core business rules and use cases
- **Data Layer**: Database access and external integrations

## Directory Structure

### Core Application
- `cmd/server/`: Application entry point and dependency injection
  - `main.go`: Server bootstrap and configuration loading
  - `wire.go`: Dependency injection definitions
  - `wire_gen.go`: Generated DI code (auto-generated)

### API Definitions
- `api/`: Protocol buffer API definitions
  - `api/user/v1/user.proto`: User service API specification
- `gen/api/`: Generated API code (auto-generated)
  - HTTP, gRPC, and validation code

### Internal Layers
- `internal/`: Private application code
  - `service/`: API implementation and request handling
  - `biz/`: Business logic and use cases
  - `data/`: Data access layer and repository implementations
  - `domain/`: Domain models and entities
  - `conf/`: Configuration structures and proto definitions
  - `server/`: Server setup (HTTP, gRPC, Kafka)
  - `utils/`: Shared utilities (logging, tracing, tenant handling)

### Database & Schema
- `gen/database/ent/`: Generated Ent ORM code (auto-generated)
  - Database client, migrations, and entity definitions

### Configuration & Deployment
- `configs/`: Configuration files
  - `config.yaml`: Application configuration
- `deploy/`: Deployment configurations
  - `kubernetes/`: K8s manifests
  - `build/`: Build-related files
- `Dockerfile`: Container definition

### Development & Build
- `Makefile`: Build automation and code generation
- `third_party/`: External proto dependencies
- `.cursor/rules/`: IDE-specific rules and guidelines

## Layer Dependencies
```
API Layer (proto definitions)
    ↓
Service Layer (internal/service)
    ↓
Business Layer (internal/biz)
    ↓
Data Layer (internal/data)
```

## Code Generation Flow
1. **Proto files** → Generated API code (`make api`)
2. **Ent schema** → Database ORM code (`make ent`)
3. **Wire definitions** → Dependency injection (`make wire`)
4. **All together** → Complete build (`make all`)

## Naming Conventions
- **Packages**: lowercase, single word when possible
- **Files**: snake_case for multi-word files
- **Proto services**: PascalCase
- **Database entities**: singular nouns
- **Repository interfaces**: `<Entity>Repo` pattern

## Multi-Tenant Architecture
- Tenant isolation implemented at the data layer
- Tenant context propagated through request lifecycle
- Database interceptors and hooks for automatic tenant filtering