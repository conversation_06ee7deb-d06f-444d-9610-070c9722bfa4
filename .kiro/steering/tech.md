# Technology Stack

## Framework & Core Libraries
- **Kratos v2**: Go microservice framework for HTTP/gRPC services
- **Go 1.24**: Primary programming language
- **Protocol Buffers**: API definition and code generation
- **Google Wire**: Dependency injection framework
- **Ent**: Database ORM and schema management
- **Watermill**: Message streaming library for Kafka integration

## Database & Storage
- **PostgreSQL**: Primary database (configurable to MySQL)
- **Redis**: Caching and session storage
- **Apache Kafka**: Message queue and event streaming

## Build System & Tools
- **Makefile**: Primary build automation
- **Docker**: Containerization support
- **protoc**: Protocol buffer compiler
- **wire**: Dependency injection code generation

## Key Dependencies
- `github.com/go-kratos/kratos/v2`: Core framework
- `entgo.io/ent`: Database ORM
- `github.com/ThreeDotsLabs/watermill`: Message streaming
- `github.com/google/wire`: Dependency injection
- `github.com/rs/zerolog`: Structured logging
- `google.golang.org/grpc`: gRPC implementation

## Common Commands

### Development Setup
```bash
# Initialize development environment
make init

# Generate all code (API, config, wire)
make all

# Generate API code from proto files
make api

# Generate configuration code
make config

# Generate Ent database code
make ent

# Generate Wire dependency injection
make wire
```

### Build & Run
```bash
# Build the application
make build

# Run the server
./bin/server -conf ./configs

# Run with Docker
docker build -t kratos-service .
docker run --rm -p 8000:8000 -p 9000:9000 kratos-service
```

### Code Generation
```bash
# Generate all Go code
go generate ./...

# Generate Wire dependencies (from cmd/server)
cd cmd/server && wire
```

## Configuration
- Configuration files in `configs/config.yaml`
- Proto-based configuration schema in `internal/conf/conf.proto`
- Environment-specific settings for DEV/PROD modes