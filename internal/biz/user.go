package biz

import (
	"context"

	"github.com/go-kratos/kratos-layout/internal/domain"
	"github.com/go-kratos/kratos/v2/log"
)

// UserUsecase 用户用例实现
type UserUsecase struct {
	repo domain.UserRepo
	log  *log.Helper
}

var _ domain.UserUsecase = (*UserUsecase)(nil)

// NewUserUsecase 创建用户用例实例
func NewUserUsecase(repo domain.UserRepo, logger log.Logger) domain.UserUsecase {
	return &UserUsecase{repo: repo, log: log.NewHelper(logger)}
}

// RegisterNewUser 注册新用户 - 业务语义命名
func (uc *UserUsecase) RegisterNewUser(ctx context.Context, u *domain.User) (*domain.User, error) {
	uc.log.WithContext(ctx).Infof("开始注册新用户: %s", u.Name)

	if err := u.Validate(); err != nil {
		uc.log.WithContext(ctx).Errorf("用户数据验证失败: %v", err)
		uc.log.
		return nil, err
	}

	// 确保ID为0，表示新增操作
	u.ID = 0
	user, err := uc.repo.Save(ctx, u)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("保存用户失败: %v", err)
		return nil, err
	}

	uc.log.WithContext(ctx).Infof("用户注册成功: ID=%d, Name=%s", user.ID, user.Name)
	return user, nil
}

// ModifyUserProfile 修改用户资料 - 业务语义命名
func (uc *UserUsecase) ModifyUserProfile(ctx context.Context, u *domain.User) error {
	uc.log.WithContext(ctx).Infof("开始修改用户资料: ID=%d", u.ID)

	if err := u.Validate(); err != nil {
		uc.log.WithContext(ctx).Errorf("用户数据验证失败: %v", err)
		return err
	}

	err := uc.repo.Update(ctx, u)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("更新用户资料失败: %v", err)
		return err
	}

	uc.log.WithContext(ctx).Infof("用户资料修改成功: ID=%d", u.ID)
	return nil
}

// DeactivateUser 停用用户 - 业务语义命名
func (uc *UserUsecase) DeactivateUser(ctx context.Context, id uint64) error {
	uc.log.WithContext(ctx).Infof("开始停用用户: ID=%d", id)

	err := uc.repo.Remove(ctx, id)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("停用用户失败: %v", err)
		return err
	}

	uc.log.WithContext(ctx).Infof("用户停用成功: ID=%d", id)
	return nil
}

// SearchUsers 搜索用户 - 业务语义命名
func (uc *UserUsecase) SearchUsers(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) {
	uc.log.WithContext(ctx).Infof("开始搜索用户: pageNum=%d, pageSize=%d", pageNum, pageSize)

	users, total, err := uc.repo.FindAll(ctx, pageNum, pageSize)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("搜索用户失败: %v", err)
		return nil, 0, err
	}

	uc.log.WithContext(ctx).Infof("用户搜索成功: 找到%d个用户，总计%d个", len(users), total)
	return users, total, nil
}

// FindUserByID 根据ID查找用户 - 业务语义命名
func (uc *UserUsecase) FindUserByID(ctx context.Context, id uint64) (*domain.User, error) {
	uc.log.WithContext(ctx).Infof("开始查找用户: ID=%d", id)

	user, err := uc.repo.FindByID(ctx, id)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("查找用户失败: %v", err)
		return nil, err
	}

	uc.log.WithContext(ctx).Infof("用户查找成功: ID=%d, Name=%s", user.ID, user.Name)
	return user, nil
}
