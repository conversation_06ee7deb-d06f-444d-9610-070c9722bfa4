package utils

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"strings"
	"time"

	kzlog "github.com/go-kratos/kratos/contrib/log/zerolog/v2"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	trangrpc "github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	zlog "github.com/rs/zerolog"
)

var globalLogger log.Logger

func NewLogger() log.Logger {
	log := zlog.New(os.Stdout).Level(zlog.DebugLevel)
	globalLogger = kzlog.NewLogger(&log)
	return globalLogger
}

func GetLogger() log.Logger {
	return globalLogger
}

type Status struct {
	Code     int32             `json:"code,omitempty"`
	Reason   string            `json:"reason,omitempty"`
	Message  string            `json:"message,omitempty"`
	Metadata map[string]string `json:"metadata,omitempty"`
}

// Server is an server logging middleware.
func LogServer(logger log.Logger) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req any) (reply any, err error) {
			var (
				kind      string
				operation string
			)
			startTime := time.Now()
			if info, ok := transport.FromServerContext(ctx); ok {
				kind = info.Kind().String()
				operation = info.Operation()
			}
			reply, err = handler(ctx, req)
			se := errors.FromError(err)
			s := Status{}
			if se != nil {
				s.Code = se.Code
				s.Reason = se.Reason
				s.Message = se.Message
				s.Metadata = se.Metadata
			}
			level, stack := extractError(err)
			filesM := []any{
				"kind", "server",
				"component", kind,
				"operation", operation,
				"args", req,
			}
			if se != nil {
				filesM = append(filesM, "error", s)
				filesM = append(filesM, "stack", stack)
			}
			filesM = append(filesM, "latency", time.Since(startTime).Seconds())
			_ = log.WithContext(ctx, logger).Log(level, filesM...)
			return
		}
	}
}

// Client is a client logging middleware.
func LogClient(logger log.Logger) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req any) (reply any, err error) {
			var (
				code      int32
				reason    string
				kind      string
				operation string
				traceId   string
			)
			var getreq any
			startTime := time.Now()
			if info, ok := transport.FromClientContext(ctx); ok {
				kind = info.Kind().String()
				operation = info.Operation()
				switch tpKind := info.Kind(); tpKind {
				case transport.KindHTTP:
					tpHttp := info.(*http.Transport)
					traceId = tpHttp.RequestHeader().Get(IstioTraceId)
					htreq := tpHttp.Request()
					if htreq != nil && htreq.Method == "GET" && req == nil {
						if len(htreq.URL.RawQuery) > 0 {
							values, err := url.ParseQuery(htreq.URL.RawQuery)
							if err != nil {
								return nil, err
							}
							// 转换成 map[string]string
							result := make(map[string]string)
							for key, value := range values {
								result[key] = value[0]
							}
							getreq = result
						}
						if getreq == nil {
							getreq, _ = extractParamsFromPath(tpHttp.PathTemplate(), htreq.URL.Path)
						}
					}
				case transport.KindGRPC:
					tpGrpc := info.(*trangrpc.Transport)
					traceId = tpGrpc.RequestHeader().Get(IstioTraceId)
				}
			}
			reply, err = handler(ctx, req)
			if se := errors.FromError(err); se != nil {
				code = se.Code
				reason = se.Reason
			}
			if req == nil {
				req = getreq
			}
			level, stack := extractError(err)
			_ = log.WithContext(ctx, logger).Log(level,
				"ts", startTime.Format(time.RFC3339),
				"kind", "client",
				"trace.id", traceId,
				"component", kind,
				"operation", operation,
				"args", req,
				"code", code,
				"reason", reason,
				"stack", stack,
				"latency", time.Since(startTime).Seconds(),
			)
			return
		}
	}
}

// extractError returns the string of the error
func extractError(err error) (log.Level, string) {
	if err != nil {
		return log.LevelError, fmt.Sprintf("%+v", err)
	}
	return log.LevelInfo, ""
}

func extractParamsFromPath(template, path string) (map[string]string, error) {
	// 将模板和路径分割为段
	templateSegments := strings.Split(template, "/")
	pathSegments := strings.Split(path, "/")

	// 检查段的数量是否匹配
	if len(templateSegments) != len(pathSegments) {
		return nil, fmt.Errorf("path segments do not match the template")
	}

	// 提取参数
	params := make(map[string]string)
	for i, segment := range templateSegments {
		if strings.HasPrefix(segment, "{") && strings.HasSuffix(segment, "}") {
			paramName := segment[1 : len(segment)-1] // 去掉大括号
			params[paramName] = pathSegments[i]
		}
	}

	return params, nil
}
