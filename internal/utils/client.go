package utils

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	ktgrpc "github.com/go-kratos/kratos/v2/transport/grpc"
	kthttp "github.com/go-kratos/kratos/v2/transport/http"
	retry "github.com/grpc-ecosystem/go-grpc-middleware/retry"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
)

func init() {
	// grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stderr, os.Stderr))
}

// DialInsecure returns an insecure GRPC connection.
func NewGrpcCli(ctx context.Context, url string, opts ...ktgrpc.ClientOption) (*grpc.ClientConn, error) {
	/*
		retryPolicy := `{
				"methodConfig": [{
					"name": [{"service": ""}],
					"retryPolicy": {
						"MaxAttempts": 10,
						"InitialBackoff": "0.1s",
						"MaxBackoff": "100s",
						"BackoffMultiplier": 1.5,
						"RetryableStatusCodes": ["UNAVAILABLE"]
					}
				}]
			}`
	*/
	return ktgrpc.DialInsecure(ctx,
		ktgrpc.WithEndpoint(url),
		ktgrpc.WithTimeout(100*time.Second),
		ktgrpc.WithMiddleware(
			recovery.Recovery(),
			metadata.Client(metadata.WithPropagatedPrefix(IstioPrefix, TENANT_ID_HEADER)),
			LogClient(GetLogger()),
		),
		/*
			ktgrpc.WithOptions(
				grpc.WithDefaultServiceConfig(retryPolicy),
			),
		*/
		ktgrpc.WithUnaryInterceptor(
			retry.UnaryClientInterceptor(
				retry.WithMax(10),
				retry.WithBackoff(retry.BackoffExponential(100*time.Millisecond)),
				retry.WithCodes(codes.Unavailable),
			),
		),
	)
}

// NewClient returns an HTTP client.
func NewHtpCli(ctx context.Context, url string, opts ...kthttp.ClientOption) (*kthttp.Client, error) {
	return kthttp.NewClient(ctx,
		kthttp.WithEndpoint(url),
		kthttp.WithTimeout(100*time.Second),
		kthttp.WithMiddleware(
			recovery.Recovery(),
			metadata.Client(metadata.WithPropagatedPrefix(IstioPrefix, TENANT_ID_HEADER)),
			LogClient(GetLogger()),
		),
	)
}
