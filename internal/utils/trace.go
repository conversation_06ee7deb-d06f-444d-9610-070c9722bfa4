package utils

import (
	"context"
	"crypto/rand"
	"fmt"
	"strings"

	"ariga.io/sqlcomment"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	trangrpc "github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"go.opencensus.io/trace"
)

const (
	IstioTraceId = "x-b3-traceid" // istio trace id
	IstioSpanId  = "x-b3-spanid"  // istio span id
)

const (
	IstioPrefix = "x-" // istio header prefix
)

// TraceID returns a traceid valuer.
func TraceID() log.Valuer {
	return func(ctx context.Context) any {
		tp, _ := transport.FromServerContext(ctx)
		traceId := ""
		if tp == nil {
			return getTraceIDFromContext(ctx)
		}
		switch tpKind := tp.Kind(); tpKind {
		case transport.KindHTTP:
			tpHttp := tp.(*http.Transport)
			traceId = tpHttp.RequestHeader().Get(IstioTraceId)
		case transport.KindGRPC:
			tpGrpc := tp.(*trangrpc.Transport)
			traceId = tpGrpc.RequestHeader().Get(IstioTraceId)
		}
		return traceId
	}
}

// SpanID returns a spanid valuer.
func SpanID() log.Valuer {
	return func(ctx context.Context) any {
		tp, _ := transport.FromServerContext(ctx)
		if tp == nil {
			return ""
		}
		spanId := ""
		switch tpKind := tp.Kind(); tpKind {
		case transport.KindHTTP:
			tpHttp := tp.(*http.Transport)
			spanId = tpHttp.RequestHeader().Get(IstioSpanId)
		case transport.KindGRPC:
			tpGrpc := tp.(*trangrpc.Transport)
			spanId = tpGrpc.RequestHeader().Get(IstioSpanId)
		}
		return spanId
	}
}

// trace middleware: 将traceId添加到返回头里
func TraceServer() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req any) (reply any, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				traceID := ""
				switch tpKind := tr.Kind(); tpKind {
				case transport.KindHTTP:
					tpHttp := tr.(*http.Transport)
					traceID = tpHttp.RequestHeader().Get(IstioTraceId)
				case transport.KindGRPC:
					tpGRPC := tr.(*grpc.Transport)
					traceID = tpGRPC.RequestHeader().Get(strings.ToLower(IstioTraceId))
				}

				if len(traceID) == 0 {
					traceID = generateTraceID().String()
					tr.RequestHeader().Set(IstioTraceId, traceID)
				}
				tr.ReplyHeader().Set("trace-id", traceID)
			}

			return handler(ctx, req)
		}
	}
}

// meta middleware: 设置metadata 默认前缀
func MetaServer() middleware.Middleware {
	return metadata.Server(metadata.WithPropagatedPrefix(IstioPrefix, TENANT_ID_HEADER))
}

// sql 注解里添加trace_id
type TraceIDCommenter struct{}

func (tr TraceIDCommenter) Tag(ctx context.Context) sqlcomment.Tags {
	fn := TraceID()
	traceId := fn(ctx)
	return sqlcomment.Tags{
		"trace_id": fmt.Sprintf("%+v", traceId),
	}
}

// UserID returns a userid valuer.
func UserID() log.Valuer {
	return func(ctx context.Context) any {
		return GetUserIDFromMeta(ctx)
	}
}

// TenantID returns a tenantid valuer.
func TenantID() log.Valuer {
	return func(ctx context.Context) any {
		return GetTenantIDFromMeta(ctx)
	}
}

// generateTraceID 生成一个随机的 TraceID
func generateTraceID() trace.TraceID {
	var traceID trace.TraceID
	_, err := rand.Read(traceID[:]) // 使用 crypto/rand 生成随机字节
	if err != nil {
		panic(fmt.Sprintf("Failed to generate TraceID: %v", err))
	}
	return traceID
}

// 生成带有trace_id的context
func NewTraceIDContext(ctx context.Context) context.Context {
	if ctx == nil {
		ctx = context.Background()
	}
	traceID := generateTraceID()
	return context.WithValue(ctx, IstioTraceId, traceID.String())
}

func getTraceIDFromContext(ctx context.Context) string {
	traceID := ctx.Value(IstioTraceId)
	if traceID == nil {
		return ""
	}
	return traceID.(string)
}
