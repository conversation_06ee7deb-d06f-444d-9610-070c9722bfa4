package utils

import (
	"context"

	"github.com/go-kratos/kratos/v2/metadata"
	"github.com/spf13/cast"
	userutil "repo.sweet7.com/backend/goutil.git/userutil"
)

const (
	TENANT_ID_HEADER = "tenant-id"
)

func GetUserIDFromMeta(ctx context.Context) string {
	return userutil.GetUserID(ctx)
}

func GetTenantIDFromMeta(ctx context.Context) uint64 {
	md, ok := metadata.FromServerContext(ctx)
	if !ok {
		return 0
	}
	tenantID := md.Get(TENANT_ID_HEADER)
	return cast.ToUint64(tenantID)
}
