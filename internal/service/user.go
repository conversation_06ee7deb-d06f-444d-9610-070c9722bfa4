package service

import (
	"context"
	"encoding/json"

	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/go-kratos/kratos-layout/gen/database/ent"
	userv1 "github.com/go-kratos/kratos-layout/gen/api/user/v1"
	"github.com/go-kratos/kratos-layout/internal/domain"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/emptypb"
)

type UserService struct {
	userv1.UnimplementedUserServer

	uc  domain.UserUsecase
	log *log.Helper
}

func NewUserService(uc domain.UserUsecase, logger log.Logger) *UserService {
	return &UserService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

func (s *UserService) HandleUserCreatedEvent(ctx context.Context, msg *message.Message) error {
	var user ent.User
	if err := json.Unmarshal(msg.Payload, &user); err != nil {
		return err
	}
	s.log.WithContext(ctx).Infof("handle user created event: %v", user)
	return nil
}

func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
	s.log.WithContext(ctx).Infof("接收到创建用户请求: Name=%s, Age=%d", req.Name, req.Age)

	user, err := s.uc.RegisterNewUser(ctx, &domain.User{
		Name:     req.Name,
		Age:      int(req.Age),
		TenantID: req.TenantId,
	})
	if err != nil {
		s.log.WithContext(ctx).Errorf("创建用户失败: %v", err)
		return nil, err
	}

	s.log.WithContext(ctx).Infof("用户创建成功: ID=%d", user.ID)
	return &userv1.CreateUserReply{
		Id: user.ID,
	}, nil
}

func (s *UserService) UpdateUser(ctx context.Context, req *userv1.UpdateUserRequest) (*userv1.UpdateUserReply, error) {
	s.log.WithContext(ctx).Infof("接收到更新用户请求: ID=%d, Name=%s", req.Id, req.Name)

	err := s.uc.ModifyUserProfile(ctx, &domain.User{
		ID:   req.Id,
		Name: req.Name,
		Age:  int(req.Age),
	})
	if err != nil {
		s.log.WithContext(ctx).Errorf("更新用户失败: %v", err)
		return nil, err
	}

	s.log.WithContext(ctx).Infof("用户更新成功: ID=%d", req.Id)
	return &userv1.UpdateUserReply{
		Success: true,
	}, nil
}

func (s *UserService) DeleteUser(ctx context.Context, req *userv1.DeleteUserRequest) (*emptypb.Empty, error) {
	s.log.WithContext(ctx).Infof("接收到删除用户请求: ID=%d", req.Id)

	err := s.uc.DeactivateUser(ctx, req.Id)
	if err != nil {
		s.log.WithContext(ctx).Errorf("删除用户失败: %v", err)
		return nil, err
	}

	s.log.WithContext(ctx).Infof("用户删除成功: ID=%d", req.Id)
	return &emptypb.Empty{}, nil
}

func (s *UserService) ListUsers(ctx context.Context, req *userv1.ListUsersRequest) (*userv1.ListUsersReply, error) {
	s.log.WithContext(ctx).Infof("接收到用户列表请求: PageNum=%d, PageSize=%d", req.PageNum, req.PageSize)

	users, total, err := s.uc.SearchUsers(ctx, int32(req.PageNum), int32(req.PageSize))
	if err != nil {
		s.log.WithContext(ctx).Errorf("获取用户列表失败: %v", err)
		return nil, err
	}

	reply := &userv1.ListUsersReply{
		Total: uint32(total),
		Users: make([]*userv1.UserInfo, 0, len(users)),
	}

	for _, u := range users {
		reply.Users = append(reply.Users, &userv1.UserInfo{
			Id:       u.ID,
			Name:     u.Name,
			Age:      int32(u.Age),
			TenantId: u.TenantID,
		})
	}

	s.log.WithContext(ctx).Infof("用户列表获取成功: 返回%d个用户，总计%d个", len(users), total)
	return reply, nil
}
