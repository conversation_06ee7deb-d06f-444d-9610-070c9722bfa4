package domain

import (
	"context"
	"errors"
)

// User 领域模型
type User struct {
	ID       uint64
	Name     string
	Age      int
	TenantID uint64
}

// UserRepo 用户仓储接口 - 使用 DDD 标准命名
type UserRepo interface {
	Save(context.Context, *User) (*User, error)                  // 保存用户（新增/更新）
	FindByID(context.Context, uint64) (*User, error)             // 根据ID查找用户
	FindAll(context.Context, int32, int32) ([]*User, int, error) // 查找用户列表
	Remove(context.Context, uint64) error                        // 移除用户
	Update(context.Context, *User) error                         // 更新用户信息
}

// UserUsecase 用户用例接口 - 使用业务语义命名
type UserUsecase interface {
	RegisterNewUser(ctx context.Context, u *User) (*User, error)                    // 注册新用户
	ModifyUserProfile(ctx context.Context, u *User) error                           // 修改用户资料
	DeactivateUser(ctx context.Context, id uint64) error                            // 停用用户
	SearchUsers(ctx context.Context, pageNum, pageSize int32) ([]*User, int, error) // 搜索用户
	FindUserByID(ctx context.Context, id uint64) (*User, error)                     // 根据ID查找用户
}

var (
	ErrNameRequired = errors.New("name is required")
	ErrAgeMustBeGt0 = errors.New("age must be greater than 0")
)

func (u *User) Validate() error {
	if len(u.Name) == 0 {
		return ErrNameRequired
	}

	if u.Age < 0 {
		return ErrAgeMustBeGt0
	}
	return nil
}
