package data

import (
	"context"
	"encoding/json"

	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/go-kratos/kratos-layout/internal/domain"
	"github.com/go-kratos/kratos/v2/log"
)

// userRepo 实现 domain.UserRepo 接口
type userRepo struct {
	data *Data
	log  *log.Helper
}

// NewUserRepo 创建用户仓储实例
func NewUserRepo(data *Data, logger log.Logger) domain.UserRepo {
	return &userRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// Save 保存用户（新增/更新）- DDD 标准命名
func (r *userRepo) Save(ctx context.Context, u *domain.User) (*domain.User, error) {
	// 如果ID为0，则为新增操作
	if u.ID == 0 {
		po, err := r.data.db.User.Create().
			SetAge(u.Age).
			SetName(u.Name).
			SetTenantID(u.TenantID).
			Save(ctx)
		if err != nil {
			return nil, err
		}

		// publish event
		payload, _ := json.Marshal(po)
		msg := message.NewMessage(watermill.NewUUID(), payload)
		if err := r.data.pub.Publish("user_created", msg); err != nil {
			r.log.Errorf("failed to publish user_created event: %v", err)
		}

		return &domain.User{
			ID:       uint64(po.ID),
			Name:     po.Name,
			Age:      po.Age,
			TenantID: po.TenantID,
		}, nil
	}

	// 否则为更新操作
	po, err := r.data.db.User.UpdateOneID(int(u.ID)).
		SetAge(u.Age).
		SetName(u.Name).
		SetTenantID(u.TenantID).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return &domain.User{
		ID:       uint64(po.ID),
		Name:     po.Name,
		Age:      po.Age,
		TenantID: po.TenantID,
	}, nil
}

// Update 更新用户信息
func (r *userRepo) Update(ctx context.Context, u *domain.User) error {
	_, err := r.data.db.User.UpdateOneID(int(u.ID)).
		SetAge(u.Age).
		SetName(u.Name).
		Save(ctx)
	return err
}

// Remove 移除用户 - DDD 标准命名
func (r *userRepo) Remove(ctx context.Context, id uint64) error {
	return r.data.db.User.DeleteOneID(int(id)).Exec(ctx)
}

// FindByID 根据ID查找用户 - DDD 标准命名
func (r *userRepo) FindByID(ctx context.Context, id uint64) (*domain.User, error) {
	po, err := r.data.db.User.Get(ctx, int(id))
	if err != nil {
		return nil, err
	}
	return &domain.User{
		ID:       uint64(po.ID),
		Name:     po.Name,
		Age:      po.Age,
		TenantID: po.TenantID,
	}, nil
}

// FindAll 查找用户列表 - DDD 标准命名
func (r *userRepo) FindAll(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) {
	total, err := r.data.db.User.Query().Count(ctx)
	if err != nil {
		return nil, 0, err
	}

	offset := (int(pageNum) - 1) * int(pageSize)
	pos, err := r.data.db.User.Query().
		Offset(offset).
		Limit(int(pageSize)).
		All(ctx)
	if err != nil {
		return nil, 0, err
	}

	var users []*domain.User
	for _, po := range pos {
		users = append(users, &domain.User{
			ID:       uint64(po.ID),
			Name:     po.Name,
			Age:      po.Age,
			TenantID: po.TenantID,
		})
	}
	return users, total, nil
}
