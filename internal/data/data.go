package data

import (
	"context"
	"fmt"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-kafka/v2/pkg/kafka"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/go-kratos/kratos-layout/internal/conf"
	"github.com/go-kratos/kratos-layout/internal/utils"

	"ariga.io/sqlcomment"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"github.com/go-kratos/kratos-layout/gen/database/ent"

	_ "github.com/lib/pq"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewUserRepo, NewEntClient, NewDriver, NewPublisher, NewSubscriber)

// Data .
type Data struct {
	db  *ent.Client
	log *log.Helper
	pub message.Publisher
	sub message.Subscriber
}

// NewData .
func NewData(entClient *ent.Client, logger log.Logger, pub message.Publisher, sub message.Subscriber) (*Data, func(), error) {
	log := log.NewHelper(logger)
	d := &Data{
		db:  entClient,
		log: log,
		pub: pub,
		sub: sub,
	}
	return d, func() {
		if err := d.db.Close(); err != nil {
			log.Error(err)
		}
		if err := pub.Close(); err != nil {
			log.Error(err)
		}
		if err := sub.Close(); err != nil {
			log.Error(err)
		}
	}, nil
}

func NewPublisher(conf *conf.Data, logger log.Logger) (message.Publisher, error) {
	// acks must be set to All for transactional producers
	pub, err := kafka.NewPublisher(
		kafka.PublisherConfig{
			Brokers:   conf.Kafka.Brokers,
			Marshaler: kafka.DefaultMarshaler{},
		},
		watermill.NewStdLogger(false, false),
	)
	if err != nil {
		return nil, err
	}
	return pub, nil
}

func NewSubscriber(conf *conf.Data, logger log.Logger) (message.Subscriber, error) {
	sub, err := kafka.NewSubscriber(
		kafka.SubscriberConfig{
			Brokers:       conf.Kafka.Brokers,
			Unmarshaler:   kafka.DefaultMarshaler{},
			ConsumerGroup: "test_group",
		},
		watermill.NewStdLogger(false, false),
	)
	if err != nil {
		return nil, err
	}
	return sub, nil
}

func NewEntClient(drv dialect.Driver, config *conf.Data) *ent.Client {
	client := ent.NewClient(ent.Driver(drv))

	if config.TenantEnabled {
		client.Intercept(utils.TenantInterceptor())
		client.Use(utils.TenantHook())
	}
	return client
}

func NewDriver(config *conf.Data, logger log.Logger) dialect.Driver {
	db, err := sql.Open(
		config.Database.Driver,
		config.Database.Source,
	)

	if err != nil {
		panic(err)
	}

	l := log.NewHelper(log.With(logger, "module", "sql"))
	var logDb dialect.Driver = db
	if config.RunMode != conf.RunMode_PROD {
		logDb = dialect.DebugWithContext(
			db,
			func(ctx context.Context, v ...any) {
				l.WithContext(ctx).Info(v)
			})
	}
	drv := sqlcomment.NewDriver(logDb,
		sqlcomment.WithTagger(
			// set trace_id in comment
			utils.TraceIDCommenter{},
		),
		sqlcomment.WithTags(sqlcomment.Tags{
			sqlcomment.KeyApplication: "your-service-name",
		}),
	)

	if err := db.DB().Ping(); err != nil {
		l.Fatalf("failed opening connection to db: %v", err)
	}
	return drv
}

func WithTx(ctx context.Context, client *ent.Client, fn func(ctx context.Context, tx *ent.Tx) error) error {
	tx, err := client.Tx(ctx)
	if err != nil {
		return err
	}
	defer func() {
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()

	txctx := ent.NewTxContext(ctx, tx)
	if err := fn(txctx, tx); err != nil {
		if rerr := tx.Rollback(); rerr != nil {
			err = fmt.Errorf("rolling back transaction: %w", rerr)
		}
		return err
	}
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("committing transaction: %w", err)
	}
	return nil
}
