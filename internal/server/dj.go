package server

import (
	"entgo.io/ent/dialect"
	"github.com/go-kratos/kratos-layout/internal/conf"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	dj "repo.sweet7.com/backend/lowcode.git/export"
)

type DJ struct{}

// register danjuan service.
func NewDJServer(c *conf.Data, drv dialect.Driver, httpSrv *http.Server, grpcSrv *grpc.Server, logger log.Logger) *DJ {
	migrateMode := dj.AppendOnly
	switch c.DjConfig.MigrateMode {
	case conf.MigrateMode_APPEND:
		migrateMode = dj.AppendOnly
	case conf.MigrateMode_DISABLED:
		migrateMode = dj.DisableAll
	case conf.MigrateMode_UNLIMITED:
		migrateMode = dj.EnableAll
	}
	if err := dj.RegisterLowcodeSrv(httpSrv, grpcSrv,
		logger, drv, dj.WithMode(migrateMode),
		dj.WithMaxPageSize(c.DjConfig.MaxPageSize),
		dj.WithSpecificFieldsRequired(c.DjConfig.SpecificFieldsRequired),
		dj.WithTenantEnabled(c.TenantEnabled)); err != nil {
		panic(err)
	}
	return &DJ{}
}
