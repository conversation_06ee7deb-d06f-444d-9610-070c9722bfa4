package server

import (
	"context"

	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos-layout/internal/service"
)

// KafkaServer is a kafka server.
type KafkaServer struct {
	sub message.Subscriber
	log *log.Helper

	userSvc *service.UserService
}

// NewKafkaServer new a kafka server.
func NewKafkaServer(sub message.Subscriber, logger log.Logger, userSvc *service.UserService) *KafkaServer {
	return &KafkaServer{
		sub: sub,
		log: log.<PERSON>elper(log.With(logger, "module", "server/kafka")),

		userSvc: userSvc,
	}
}

// Start start the kafka server.
func (s *KafkaServer) Start(ctx context.Context) error {
	messages, err := s.sub.Subscribe(ctx, "user_created")
	if err != nil {
		return err
	}

	go func() {
		for msg := range messages {
			s.log.Infof("received message: %s", string(msg.Payload))

			if err := s.userSvc.HandleUserCreatedEvent(ctx, msg); err != nil {
				s.log.Errorf("failed to handle user_created event: %v", err)
			}

			msg.Ack()
		}
	}()

	return nil
}

// Stop stop the kafka server.
func (s *KafkaServer) Stop(ctx context.Context) error {
	return s.sub.Close()
}
