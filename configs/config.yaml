server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
data:
  # 服务运行模式: DEV,PROD
  run_mode: DEV
  database:
    #driver: mysql
    #source: root:root@tcp(127.0.0.1:3306)/test?parseTime=True&loc=Local
    driver: postgres
    source: host=127.0.0.1 port=5432 user=lowcode password=lowcode dbname=test sslmode=disable
  redis:
    addr: 127.0.0.1:6379
    read_timeout: 0.2s
    write_timeout: 0.2s
  # 低代码配置
  dj_config:
    # 最大分页条数,0 为不限制
    max_page_size: 0
    # fields 是否必须指定
    specific_fields_required: false
    # schema migrate mode: DISABLED, UNLIMITED, APPEND
    migrate_mode: UNLIMITED
  # 是否启用多租户
  tenant_enabled: true
  kafka:
    brokers:
      - 127.0.0.1:9092
