# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: User API
    description: 用户服务
    version: 0.0.1
paths:
    /v1/users:
        get:
            tags:
                - User
            description: 获取用户列表
            operationId: User_ListUsers
            parameters:
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: uint32
                - name: pageNum
                  in: query
                  schema:
                    type: integer
                    format: uint32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.ListUsersReply'
        post:
            tags:
                - User
            description: 创建用户
            operationId: User_CreateUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.CreateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.CreateUserReply'
    /v1/users/{id}:
        put:
            tags:
                - User
            description: 更新用户
            operationId: User_UpdateUser
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.UpdateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.UpdateUserReply'
        delete:
            tags:
                - User
            description: 删除用户
            operationId: User_DeleteUser
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
components:
    schemas:
        user.v1.CreateUserReply:
            type: object
            properties:
                id:
                    type: string
            description: 创建用户响应
        user.v1.CreateUserRequest:
            type: object
            properties:
                name:
                    type: string
                age:
                    type: integer
                    format: int32
                tenantId:
                    type: string
            description: 创建用户请求
        user.v1.ListUsersReply:
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/user.v1.UserInfo'
                total:
                    type: integer
                    format: uint32
            description: 用户列表响应
        user.v1.UpdateUserReply:
            type: object
            properties:
                success:
                    type: boolean
            description: 更新用户响应
        user.v1.UpdateUserRequest:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                age:
                    type: integer
                    format: int32
            description: 更新用户请求
        user.v1.UserInfo:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                age:
                    type: integer
                    format: int32
                tenantId:
                    type: string
            description: 用户信息
tags:
    - name: User
