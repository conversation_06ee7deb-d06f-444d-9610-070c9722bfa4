// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos-layout/internal/biz"
	"github.com/go-kratos/kratos-layout/internal/conf"
	"github.com/go-kratos/kratos-layout/internal/data"
	"github.com/go-kratos/kratos-layout/internal/server"
	"github.com/go-kratos/kratos-layout/internal/service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	driver := data.NewDriver(confData, logger)
	client := data.NewEntClient(driver, confData)
	publisher, err := data.NewPublisher(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	subscriber, err := data.NewSubscriber(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	dataData, cleanup, err := data.NewData(client, logger, publisher, subscriber)
	if err != nil {
		return nil, nil, err
	}
	userRepo := data.NewUserRepo(dataData, logger)
	userUsecase := biz.NewUserUsecase(userRepo, logger)
	userService := service.NewUserService(userUsecase, logger)
	grpcServer := server.NewGRPCServer(confServer, userService, logger)
	httpServer := server.NewHTTPServer(confServer, userService, logger)
	kafkaServer := server.NewKafkaServer(subscriber, logger, userService)
	dj := server.NewDJServer(confData, driver, httpServer, grpcServer, logger)
	faker := server.NewFakeServer(dj)
	app := newApp(logger, grpcServer, httpServer, kafkaServer, faker)
	return app, func() {
		cleanup()
	}, nil
}
